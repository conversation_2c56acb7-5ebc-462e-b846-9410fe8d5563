#!/usr/bin/env python3
"""
Скрипт для удаления всех групп из базы данных
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import GroupRepository


async def delete_all_groups():
    """Удалить все группы из базы данных"""
    try:
        print("🗑️ Начинаем удаление всех групп...")
        
        # Получаем количество групп перед удалением
        all_groups = await GroupRepository.get_all()
        total_groups = len(all_groups)
        
        if total_groups == 0:
            print("ℹ️ Групп для удаления не найдено")
            return
        
        print(f"📊 Найдено групп для удаления: {total_groups}")
        
        # Подтверждение
        confirm = input(f"⚠️ Вы уверены, что хотите удалить ВСЕ {total_groups} групп? (yes/no): ")
        if confirm.lower() not in ['yes', 'y', 'да']:
            print("❌ Удаление отменено")
            return
        
        # Удаляем все группы
        deleted_count = await GroupRepository.delete_all()
        
        if deleted_count > 0:
            print(f"✅ Успешно удалено {deleted_count} групп")
            print("✅ Все связи групп с преподавателями, кураторами и студентами также удалены")
        else:
            print("⚠️ Группы не были удалены")
            
    except Exception as e:
        print(f"❌ Ошибка при удалении групп: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(delete_all_groups())
