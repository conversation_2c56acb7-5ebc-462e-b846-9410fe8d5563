#!/usr/bin/env python3
"""
Тестовый скрипт для проверки исправлений в магазине
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database, ShopItemRepository, StudentRepository, StudentPurchaseRepository


async def test_shop_fixes():
    """Тестирование исправлений в магазине"""
    print("🧪 Тестирование исправлений магазина...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Тест 1: Создание бонусного задания
        print("\n📝 Тест 1: Создание бонусного задания")
        task = await ShopItemRepository.create_bonus_task(
            name="Тестовое бонусное задание",
            description="Это тестовое описание бонусного задания для проверки покупки.",
            price=75
        )
        print(f"✅ Создано задание: {task.name} (ID: {task.id}, Цена: {task.price})")
        
        # Тест 2: Получение студента для тестирования
        print("\n👤 Тест 2: Поиск тестового студента")
        # Ищем любого студента в базе
        students = await StudentRepository.get_all()
        if not students:
            print("❌ В базе нет студентов для тестирования")
            return

        student = students[0]  # Берем первого студента
        print(f"✅ Найден студент: ID {student.id}")
        
        # Тест 3: Проверка метода has_purchased_item (должен вернуть False)
        print(f"\n🔍 Тест 3: Проверка покупки задания (до покупки)")
        has_purchased_before = await StudentPurchaseRepository.has_purchased_item(student.id, task.id)
        print(f"✅ Покупал ли задание до покупки: {has_purchased_before} (должно быть False)")
        
        # Тест 4: Симуляция покупки
        print(f"\n💳 Тест 4: Симуляция покупки задания")
        # Проверяем баланс
        balance = await StudentRepository.get_balance(student.id)
        print(f"   Баланс студента: {balance['coins']} монет")
        
        if balance['coins'] >= task.price:
            # Выполняем покупку
            success = await StudentRepository.spend_coins(student.id, task.price)
            if success:
                purchase = await StudentPurchaseRepository.create_purchase(student.id, task.id, task.price)
                print(f"✅ Покупка выполнена: ID {purchase.id}")
                
                # Проверяем новый баланс
                new_balance = await StudentRepository.get_balance(student.id)
                print(f"   Новый баланс: {new_balance['coins']} монет")
            else:
                print("❌ Ошибка при списании монет")
        else:
            print(f"❌ Недостаточно монет для покупки (нужно {task.price}, есть {balance['coins']})")
            # Добавляем монеты для теста
            await StudentRepository.add_coins(student.id, task.price)
            print(f"   Добавлено {task.price} монет для теста")
            
            # Повторяем покупку
            success = await StudentRepository.spend_coins(student.id, task.price)
            if success:
                purchase = await StudentPurchaseRepository.create_purchase(student.id, task.id, task.price)
                print(f"✅ Покупка выполнена: ID {purchase.id}")
        
        # Тест 5: Проверка метода has_purchased_item (должен вернуть True)
        print(f"\n🔍 Тест 5: Проверка покупки задания (после покупки)")
        has_purchased_after = await StudentPurchaseRepository.has_purchased_item(student.id, task.id)
        print(f"✅ Покупал ли задание после покупки: {has_purchased_after} (должно быть True)")
        
        # Тест 6: Получение покупок студента
        print(f"\n📦 Тест 6: Получение покупок студента")
        purchases = await StudentPurchaseRepository.get_student_purchases(student.id)
        print(f"✅ Всего покупок у студента: {len(purchases)}")
        for purchase in purchases:
            print(f"   - {purchase.item.name} ({purchase.price_paid} монет)")
        
        # Тест 7: Проверка попытки повторной покупки
        print(f"\n🚫 Тест 7: Попытка повторной покупки")
        has_purchased_again = await StudentPurchaseRepository.has_purchased_item(student.id, task.id)
        if has_purchased_again:
            print("✅ Система корректно определяет, что товар уже куплен")
        else:
            print("❌ Ошибка: система не определяет покупку")
        
        print("\n🎉 Все тесты завершены!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_shop_fixes())
