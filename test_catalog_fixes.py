#!/usr/bin/env python3
"""
Тестовый скрипт для проверки исправлений каталога
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database, ShopItemRepository, StudentRepository, StudentPurchaseRepository, BonusTestRepository, StudentBonusTestRepository


async def test_catalog_filtering():
    """Тестирование фильтрации каталога"""
    print("🧪 Тестирование фильтрации каталога...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Получаем студента для тестирования
        students = await StudentRepository.get_all()
        if not students:
            print("❌ В базе нет студентов для тестирования")
            return
        
        student = students[0]
        print(f"✅ Найден студент: ID {student.id}")
        
        # Тест 1: Получение всех товаров
        print("\n📦 Тест 1: Получение всех товаров")
        shop_items = await ShopItemRepository.get_all_active()
        bonus_tests = await BonusTestRepository.get_all()
        print(f"✅ Всего товаров в магазине: {len(shop_items)}")
        print(f"✅ Всего бонусных тестов: {len(bonus_tests)}")
        
        # Тест 2: Получение купленных товаров студента
        print(f"\n🛒 Тест 2: Получение покупок студента")
        purchased_items = await StudentPurchaseRepository.get_student_purchases(student.id)
        purchased_bonus_tests = await StudentBonusTestRepository.get_student_bonus_tests(student.id)
        print(f"✅ Купленных товаров: {len(purchased_items)}")
        print(f"✅ Купленных бонусных тестов: {len(purchased_bonus_tests)}")
        
        # Показываем купленные товары
        if purchased_items:
            print("   Купленные товары:")
            for purchase in purchased_items:
                print(f"   - {purchase.item.name} (ID: {purchase.item_id})")
        
        if purchased_bonus_tests:
            print("   Купленные бонусные тесты:")
            for purchase in purchased_bonus_tests:
                print(f"   - {purchase.bonus_test.name} (ID: {purchase.bonus_test_id})")
        
        # Тест 3: Фильтрация каталога
        print(f"\n🔍 Тест 3: Фильтрация каталога")
        
        # Получаем ID купленных товаров
        purchased_item_ids = {purchase.item_id for purchase in purchased_items}
        purchased_bonus_test_ids = {purchase.bonus_test_id for purchase in purchased_bonus_tests}
        
        print(f"   ID купленных товаров: {purchased_item_ids}")
        print(f"   ID купленных бонусных тестов: {purchased_bonus_test_ids}")
        
        # Фильтруем каталог
        available_items = []
        
        # Добавляем доступные товары
        for item in shop_items:
            if item.item_type != "bonus_test" and item.id not in purchased_item_ids:
                available_items.append({
                    'type': 'shop_item',
                    'id': item.id,
                    'name': item.name,
                    'price': item.price,
                    'item_type': item.item_type
                })
        
        # Добавляем доступные бонусные тесты
        for test in bonus_tests:
            if test.id not in purchased_bonus_test_ids:
                question_count = len(test.questions) if test.questions else 0
                available_items.append({
                    'type': 'bonus_test',
                    'id': test.id,
                    'name': f"{test.name} ({question_count} вопр.)",
                    'price': test.price,
                    'item_type': 'bonus_test'
                })
        
        print(f"✅ Доступных товаров в каталоге: {len(available_items)}")
        
        if available_items:
            print("   Доступные товары:")
            for item in available_items:
                print(f"   - {item['name']} ({item['price']} монет, тип: {item['item_type']})")
        else:
            print("   📝 Все товары уже куплены!")
        
        # Тест 4: Проверка логики фильтрации
        print(f"\n✅ Тест 4: Проверка логики фильтрации")
        total_items = len(shop_items) + len(bonus_tests)
        total_purchased = len(purchased_items) + len(purchased_bonus_tests)
        expected_available = total_items - total_purchased
        
        # Учитываем, что товары типа "bonus_test" в shop_items не показываются
        bonus_test_items_in_shop = len([item for item in shop_items if item.item_type == "bonus_test"])
        expected_available -= bonus_test_items_in_shop
        
        print(f"   Всего товаров: {total_items}")
        print(f"   Товаров типа 'bonus_test' в магазине (исключаются): {bonus_test_items_in_shop}")
        print(f"   Всего куплено: {total_purchased}")
        print(f"   Ожидается доступных: {expected_available}")
        print(f"   Фактически доступных: {len(available_items)}")
        
        if len(available_items) == expected_available:
            print("✅ Фильтрация работает корректно!")
        else:
            print("⚠️ Возможно есть расхождения в логике фильтрации")
        
        print("\n🎉 Все тесты завершены!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_catalog_filtering())
