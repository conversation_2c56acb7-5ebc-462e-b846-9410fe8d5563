#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы с бонусными заданиями
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database, ShopItemRepository


async def test_bonus_tasks():
    """Тестирование функций бонусных заданий"""
    print("🧪 Тестирование бонусных заданий...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Тест 1: Создание бонусного задания
        print("\n📝 Тест 1: Создание бонусного задания")
        task = await ShopItemRepository.create_bonus_task(
            name="Тестовое задание",
            description="Это тестовое описание бонусного задания для проверки работы системы.",
            price=50
        )
        print(f"✅ Создано задание: {task.name} (ID: {task.id}, Цена: {task.price})")
        
        # Тест 2: Получение всех бонусных заданий
        print("\n📋 Тест 2: Получение всех бонусных заданий")
        bonus_tasks = await ShopItemRepository.get_by_type("bonus_task")
        print(f"✅ Найдено заданий: {len(bonus_tasks)}")
        for task in bonus_tasks:
            print(f"   - {task.name} ({task.price} монет)")
        
        # Тест 3: Получение задания по ID
        print(f"\n🔍 Тест 3: Получение задания по ID {task.id}")
        found_task = await ShopItemRepository.get_by_id(task.id)
        if found_task:
            print(f"✅ Найдено: {found_task.name}")
            print(f"   Описание: {found_task.description}")
            print(f"   Тип: {found_task.item_type}")
            print(f"   Активно: {found_task.is_active}")
        else:
            print("❌ Задание не найдено")
        
        # Тест 4: Деактивация задания
        print(f"\n🗑 Тест 4: Деактивация задания {task.id}")
        success = await ShopItemRepository.deactivate(task.id)
        if success:
            print("✅ Задание деактивировано")
            
            # Проверяем, что задание больше не активно
            updated_task = await ShopItemRepository.get_by_id(task.id)
            if updated_task and not updated_task.is_active:
                print("✅ Статус задания изменен на неактивный")
            else:
                print("❌ Статус задания не изменился")
        else:
            print("❌ Не удалось деактивировать задание")
        
        # Тест 5: Проверка, что деактивированное задание не показывается в активных
        print("\n📋 Тест 5: Проверка активных заданий после деактивации")
        active_tasks = await ShopItemRepository.get_by_type("bonus_task")
        print(f"✅ Активных заданий: {len(active_tasks)}")
        
        print("\n🎉 Все тесты завершены!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_bonus_tasks())
